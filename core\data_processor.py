#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理器核心模块
整合所有功能模块，提供统一的数据处理接口
"""

from collections import defaultdict
from PyQt6.QtCore import QThread, pyqtSignal
import gc
import pandas as pd # Added for validate_data_sufficiency

from core.file_manager import FileManager
from core.allocation_engine import AllocationEngine
from core.excel_generator import ExcelGenerator
from utils.helpers import get_available_staff, performance_monitor


class DataProcessor(QThread):
    """数据处理器核心类"""
    
    # 信号定义
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished_signal = pyqtSignal(bool, str)
    allocation_data_ready = pyqtSignal(dict)  # 新增：分配数据准备完成信号
    
    def __init__(self, assignment_file, daily_work_file, operation_files, output_folder):
        super().__init__()
        self.assignment_file = assignment_file
        self.daily_work_file = daily_work_file
        self.operation_files = operation_files
        self.output_folder = output_folder
        
        # 初始化各个模块
        self.file_manager = FileManager(status_callback=self.emit_status)
        self.allocation_engine = AllocationEngine(status_callback=self.emit_status)
        self.excel_generator = ExcelGenerator(status_callback=self.emit_status)
    
    def emit_status(self, message):
        """发送状态消息"""
        # 过滤掉不必要的详细信息
        if self._should_filter_message(message):
            return
            
        # 对重要信息进行格式化突出显示
        message = self._format_message(message)
        
        # 发送状态消息
        self.status_updated.emit(message)
    
    def _should_filter_message(self, message):
        """判断是否应该过滤掉某些消息"""
        # 过滤掉一些不必要的详细信息
        filter_patterns = [
            "使用视图而不是复制",
            "保持原始编码格式",
            "纯数字编码提取完成",
            "尝试二次匹配",
            "使用所有二次匹配编码查找",
            "通过二次匹配找到手机名称",
            "原始文件尺寸:",
            "字体:",
            "填充:",
            "保持原始编码格式:"
        ]
        
        # 检查是否包含需要过滤的模式
        for pattern in filter_patterns:
            if pattern in message:
                return True
                
        return False
    
    def _format_message(self, message):
        """格式化消息，突出显示重要信息"""
        # 突出显示重要信息
        highlight_patterns = {
            "开始数据处理": "🚀 开始数据处理",
            "处理完成": "✅ 处理完成",
            "分配了": "✅ 分配了",
            "发现": "⚠️ 发现",
            "警告": "⚠️ 警告",
            "错误": "❌ 错误",
            "失败": "❌ 失败"
        }
        
        # 替换突出显示的模式
        for pattern, replacement in highlight_patterns.items():
            if pattern in message and not message.startswith(replacement.split()[0]):
                message = message.replace(pattern, replacement)
        
        # 简化某些消息
        if "对应的每日工作编码" in message or "对应的纯数字编码" in message:
            # 截断过长的编码列表
            parts = message.split(":")
            if len(parts) > 1 and len(parts[1]) > 50:
                message = f"{parts[0]}: [编码列表已省略]"
        
        return message
    
    def emit_progress(self, value):
        """发送进度消息"""
        self.progress_updated.emit(value)
    
    def validate_data_sufficiency(self, daily_work_df, operation_data):
        """
        验证每日工作总表中的数据量是否足够分配给运营表格
        
        Args:
            daily_work_df: 每日工作总表DataFrame
            operation_data: 运营表格数据字典
            
        Returns:
            bool: 数据是否足够
            dict: 不足的运营编码及其数据差异
        """
        if daily_work_df is None or len(daily_work_df) == 0 or '运营编码' not in daily_work_df.columns:
            self.emit_status("⚠️ 每日工作总表为空或缺少运营编码列，无法验证数据充足性")
            return True, {}
            
        insufficient_codes = {}
        all_sufficient = True
        
        # 从每日工作总表中获取每个运营编码的数据量
        daily_code_counts = daily_work_df['运营编码'].astype(str).value_counts()
        
        # 提取纯数字编码用于匹配
        daily_work_df['运营编码_纯数字'] = daily_work_df['运营编码'].astype(str).apply(
            lambda x: ''.join(filter(str.isdigit, x))
        )
        daily_digit_counts = daily_work_df['运营编码_纯数字'].value_counts()
        
        for operation_code, op_info in operation_data.items():
            # 获取运营表格中该编码的数据量（过滤空白行后）
            data_rows = op_info['data_rows']
            non_empty_rows = data_rows.apply(lambda row: not all(
                pd.isna(val) or (isinstance(val, str) and val.strip() == '') 
                for val in row
            ), axis=1)
            filtered_data_rows = data_rows[non_empty_rows]
            operation_data_count = len(filtered_data_rows)
            
            # 获取每日工作总表中对应的数据量
            daily_count = 0
            
            # 1. 直接匹配
            if operation_code in daily_code_counts:
                daily_count = daily_code_counts[operation_code]
            else:
                # 2. 尝试纯数字匹配
                digit_code = ''.join(filter(str.isdigit, operation_code))
                if digit_code and digit_code in daily_digit_counts:
                    daily_count = daily_digit_counts[digit_code]
                else:
                    # 3. 检查是否有二次匹配信息
                    if 'secondary_match' in op_info:
                        match_info = op_info['secondary_match']
                        if 'all_matches' in match_info:
                            # 有多个匹配，累加所有匹配的数量
                            for matched_code in match_info['all_matches']:
                                if matched_code in daily_code_counts:
                                    daily_count += daily_code_counts[matched_code]
                        elif 'matched_code' in match_info:
                            # 单个匹配
                            matched_code = match_info['matched_code']
                            if matched_code in daily_code_counts:
                                daily_count = daily_code_counts[matched_code]
            
            # 检查数据量是否足够
            if daily_count < operation_data_count and daily_count > 0:
                insufficient_codes[operation_code] = {
                    'daily_count': daily_count,
                    'operation_count': operation_data_count,
                    'difference': operation_data_count - daily_count
                }
                all_sufficient = False
        
        return all_sufficient, insufficient_codes
    
    @performance_monitor
    def run(self):
        """主处理流程 - 性能优化版本"""
        try:
            self.emit_status("开始数据处理...")
            self.emit_progress(0)

            # 1. 读取文件
            self.emit_status("正在读取文件...")
            assignment_df = self.file_manager.read_assignment_file(self.assignment_file)
            daily_work_df = self.file_manager.read_daily_work_file(self.daily_work_file)
            operation_data = self.file_manager.read_operation_files(self.operation_files)
            self.emit_progress(20)

            # 性能优化：强制垃圾回收释放内存
            gc.collect()
            
            # 2. 获取可用客服
            available_staff = get_available_staff(assignment_df)
            self.emit_status(f"在班客服: {', '.join(available_staff)}")
            self.emit_progress(25)
            
            # 3. 验证每日工作总表数据是否足够
            self.emit_status("验证每日工作总表数据充足性...")
            data_sufficient, insufficient_codes = self.validate_data_sufficiency(daily_work_df, operation_data)
            if not data_sufficient:
                insufficient_count = len(insufficient_codes)
                total_difference = sum(info['difference'] for info in insufficient_codes.values())
                self.emit_status(f"⚠️ 发现 {insufficient_count} 个运营编码的每日工作数据量不足，共缺少 {total_difference} 条数据")
                self.emit_status("⚠️ 详细信息:")
                for code, info in insufficient_codes.items():
                    self.emit_status(f"  - 运营编码 '{code}': 每日工作数据量 {info['daily_count']}，运营表格数据量 {info['operation_count']}，缺少 {info['difference']} 条")
                self.emit_status("⚠️ 请确保每日工作总表中的编码数量充足，否则可能导致分配不平衡")
            else:
                self.emit_status("✅ 每日工作总表数据充足性验证通过")
            
            # 4. 处理未匹配的运营编码
            unmatched_codes = self.file_manager.find_unmatched_operations(daily_work_df, operation_data)
            unmatched_data = []
            if unmatched_codes:
                self.emit_status(f"发现 {len(unmatched_codes)} 个未匹配的运营编码: {', '.join(unmatched_codes)}")
                unmatched_data = self.file_manager.move_unmatched_to_separate_data_with_analysis(
                    operation_data, unmatched_codes, daily_work_df)
            self.emit_progress(30)
            
            # 5. 分析手机和运营编码的关系
            self.emit_status("分析手机分配关系...")
            phone_operation_mapping = self.allocation_engine.analyze_phone_operation_mapping(
                daily_work_df, operation_data, unmatched_data)
            self.emit_progress(40)
            
            # 6. 执行数据分配
            self.emit_status("开始数据分配...")
            allocated_data = {}
            staff_operation_codes = defaultdict(set)
            # 创建手机到客服的映射，确保同一个手机只分配给一个客服
            phone_to_staff = {}
            
            if phone_operation_mapping:
                # 使用手机分组分配
                self.emit_status("使用手机分组分配模式")
                self.allocation_engine.allocate_by_phone_groups(
                    operation_data, phone_operation_mapping, available_staff,
                    allocated_data, staff_operation_codes, unmatched_data, daily_work_df, phone_to_staff)
            else:
                # 尝试从运营表格中推断手机分组
                self.emit_status("尝试从运营表格推断手机分组...")
                inferred_phone_mapping = self.allocation_engine.infer_phone_groups_from_operation_data(operation_data)

                if inferred_phone_mapping:
                    self.emit_status("使用推断的手机分组分配模式")
                    self.allocation_engine.allocate_by_phone_groups(
                        operation_data, inferred_phone_mapping, available_staff,
                        allocated_data, staff_operation_codes, unmatched_data, daily_work_df, phone_to_staff)
                else:
                    # 最后的备用方案：按运营编码整体分配（避免拆分同一手机）
                    self.emit_status("使用运营编码整体分配模式（保持手机完整性）")
                    self.allocation_engine.allocate_by_operation_groups(
                        operation_data, available_staff,
                        allocated_data, staff_operation_codes, unmatched_data, daily_work_df, phone_to_staff)
            self.emit_progress(70)
            
            # 7. 生成Excel文件
            self.emit_status("生成Excel文件...")
            self.excel_generator.generate_excel_files_with_daily_work(
                allocated_data, self.output_folder, unmatched_data,
                staff_operation_codes, daily_work_df, operation_data)
            self.emit_progress(90)

            # 8. 准备搜索数据
            self.emit_status("准备搜索数据...")
            search_data = self.prepare_search_data(allocated_data, staff_operation_codes, daily_work_df)
            self.allocation_data_ready.emit(search_data)
            self.emit_progress(100)

            # 9. 完成
            total_allocated = len(allocated_data)
            total_unmatched = len(unmatched_data)

            success_message = f"处理完成！分配了 {total_allocated} 个数据块"
            if total_unmatched > 0:
                success_message += f"，{total_unmatched} 条数据未匹配"

            self.emit_status(success_message)
            self.finished_signal.emit(True, success_message)
            
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            error_message = f"处理失败: {str(e)}"
            self.emit_status(error_message)
            self.emit_status(f"详细错误信息: {error_details}")
            self.finished_signal.emit(False, error_message)

    def prepare_search_data(self, allocated_data, staff_operation_codes, daily_work_df):
        """准备搜索数据"""
        search_data = {}

        try:
            # 遍历每个客服的分配数据
            for staff_name, staff_codes in staff_operation_codes.items():
                search_data[staff_name] = {}

                for operation_code in staff_codes:
                    # 获取数据量
                    data_count = 0
                    if staff_name in allocated_data:
                        for code, data_info in allocated_data[staff_name].items():
                            if operation_code in data_info.get('operation_codes', set()):
                                data_count += len(data_info.get('data_rows', []))

                    # 获取手机名称
                    phone_name = self.get_phone_name_for_code(operation_code, daily_work_df)

                    search_data[staff_name][operation_code] = {
                        'data_count': data_count,
                        'phone_name': phone_name,
                        'staff_name': staff_name
                    }

            self.emit_status(f"搜索数据准备完成，共 {sum(len(codes) for codes in search_data.values())} 个运营编码")
            return search_data

        except Exception as e:
            self.emit_status(f"准备搜索数据失败: {str(e)}")
            return {}

    def get_phone_name_for_code(self, operation_code, daily_work_df):
        """获取运营编码对应的手机名称"""
        try:
            if daily_work_df is None or '运营编码' not in daily_work_df.columns or '手机名称' not in daily_work_df.columns:
                return "未知"

            # 直接匹配
            matches = daily_work_df[daily_work_df['运营编码'].astype(str).str.strip() == operation_code]
            if not matches.empty:
                phone_name = matches.iloc[0]['手机名称']
                return str(phone_name) if pd.notna(phone_name) else "未知"

            # 数字匹配
            operation_digits = ''.join(filter(str.isdigit, operation_code))
            if operation_digits:
                for _, row in daily_work_df.iterrows():
                    daily_code = str(row['运营编码']).strip()
                    daily_digits = ''.join(filter(str.isdigit, daily_code))
                    if daily_digits == operation_digits:
                        phone_name = row['手机名称']
                        return str(phone_name) if pd.notna(phone_name) else "未知"

            return "未知"

        except Exception:
            return "未知"
