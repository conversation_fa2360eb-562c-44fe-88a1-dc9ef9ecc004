# 评价任务拆分工具 v2.0

## 📖 项目简介

评价任务拆分工具是一个专业的数据处理工具，用于将评价数据按客服智能分配并生成对应的Excel文件。基于PyQt6开发，具有现代化的用户界面和强大的数据处理能力。

## 功能特点

1. **选择客服排班**: 选择客服排班表，用于评价分配给在班客服
2. **运营表格处理**: 读取运营表格，按照特定格式处理：
   - 第1行：A1=商品名称，B1=运营编码
   - 第2行：A2=主评，B2=追评，C2=原始单号，D2=备注
   - 第3行以下：具体的评价数据
3. **智能分配**: 采用手机分组优先的分配策略，避免设备使用冲突
4. **手机分组**: 同一手机的所有运营编码分配给同一个客服
5. **样式保持**: 完全保持原始Excel文件的样式，包括字体、颜色、边框、对齐等
6. **格式完整**: 输出时保持原始表格的完整结构和表头信息
7. **分客服输出**: 每个客服生成独立的Excel汇总文件
8. **分表管理**: 每个客服文件内，不同运营编码对应不同Sheet
9. **工作总表**: 自动生成每个客服对应的每日工作总表
10. **多文件支持**: 支持同时处理多个运营表格文件

## 安装依赖

```bash
pip install -r requirements.txt
```

## 运行程序

```bash
python main.py
```

## 使用说明

### 1. 数据准备
- **客服排班**: Excel格式的客服排班表，第一列为客服姓名
- **运营表格**: 一个或多个Excel文件，格式要求：
  - 第1行：A1=商品名称，B1=运营编码（如：GL-3755615211642552348-A）
  - 第2行：A2=主评，B2=追评，C2=原始单号，D2=备注
  - 第3行以下：具体的评价数据行

### 2. 操作步骤
1. 点击"选择客服排班"选择客服排班文件
2. 点击"选择运营表格(可多选)"选择一个或多个运营表格文件
3. 点击"选择输出文件夹"指定结果保存位置
4. 点击"开始拆分"开始处理

### 3. 输出结果
- **客服姓名_评价汇总MM.dd.xlsx**: 每个客服的评价数据汇总文件（包含日期）
- **客服姓名_每日工作总表MM.dd.xlsx**: 每个客服对应运营编码的每日工作数据（包含日期）
- 评价汇总文件内部，不同运营编码对应不同的Sheet
- **未匹配数据.xlsx**: 包含无法分配的数据（如果有的话）

**文件名示例**：
- 敬业_评价汇总07.31.xlsx
- 敬业_每日工作总表07.31.xlsx

## 数据处理规则

1. **运营编码提取**: 从运营表格B1单元格提取运营编码
2. **数据分配**: 将第3行以下的评价数据平均分配给在班客服
3. **格式保持**: 每个输出表格都包含完整的表头（前两行）
4. **分表策略**: 每个运营编码+客服组合生成一个独立的Sheet
5. **数据量控制**: 当运营表格的评价数量大于每日工作总表中对应编码的数量时，只匹配每日工作总表中有的数量，剩余的放入未匹配数据分析报告
6. **空白行过滤**: 自动过滤运营表格中的空白行（所有单元格都为空或只包含空格的行）

## 注意事项

1. 确保Excel文件格式正确，特别是列名要准确
2. 运营编码格式支持：
   - 字母-数字-字母的组合（如：GL-3755615211642552348-A）
   - 字母-数字-字母数字组合的格式（如：DLS-10029969879149-A1, JSB-10026890480657-A2）
3. 程序支持处理大量数据，但建议文件大小不超过100MB
4. 输出文件会覆盖同名文件，请注意备份

## 技术特点

- 使用PyQt6构建现代化GUI界面
- 多线程处理，避免界面卡顿
- 实时进度显示和日志记录
- 支持多文件批量处理
- 智能客服姓名识别
- 完整保持原始表格格式
- 轮询分配算法确保数据均匀分布

## 项目文件说明

- `main.py` - 主程序文件
- `requirements.txt` - 依赖包列表
- `run.bat` - Windows启动脚本
- `test_data_generator.py` - 测试数据生成器
- `test_logic.py` - 逻辑测试脚本
- `test_gui.py` - GUI测试脚本
- `使用说明.md` - 详细使用说明
- `README.md` - 项目说明文档
