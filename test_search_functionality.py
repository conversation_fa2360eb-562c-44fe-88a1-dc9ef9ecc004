#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试搜索功能
"""

import sys
from PyQt6.QtWidgets import QApplication
from gui.main_window import MainWindow

def test_search_functionality():
    """测试搜索功能"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = MainWindow()
    
    # 模拟分配数据
    test_allocation_data = {
        "敬业": {
            "GL-3755615211642552348-A": {
                "data_count": 150,
                "phone_name": "iPhone 13",
                "staff_name": "敬业"
            },
            "ZDDWY-3759694135590781088": {
                "data_count": 200,
                "phone_name": "小米12",
                "staff_name": "敬业"
            }
        },
        "小王": {
            "DLS-10029969879149-A1": {
                "data_count": 180,
                "phone_name": "华为P50",
                "staff_name": "小王"
            },
            "JSB-10026890480657-A2": {
                "data_count": 120,
                "phone_name": "OPPO Find X5",
                "staff_name": "小王"
            }
        }
    }
    
    # 更新分配数据
    window.update_allocation_data(test_allocation_data)
    
    print("✅ 搜索功能测试数据已加载")
    print("📋 测试数据包含:")
    for staff, codes in test_allocation_data.items():
        print(f"  客服 {staff}:")
        for code, info in codes.items():
            print(f"    - {code}: {info['data_count']}条数据, {info['phone_name']}")
    
    print("\n🔍 可以测试以下搜索:")
    print("  - GL-3755615211642552348-A (精确匹配)")
    print("  - GL (模糊匹配)")
    print("  - 3755615211642552348 (部分匹配)")
    print("  - ZDDWY (模糊匹配)")
    print("  - 不存在的编码 (无匹配)")
    
    # 显示窗口
    window.show()
    
    return app.exec()

if __name__ == "__main__":
    test_search_functionality()
