#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口GUI模块
"""

import sys
import os
import pandas as pd
from pathlib import Path
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                            QWidget, QPushButton, QLabel, QLineEdit, QTextEdit,
                            QProgressBar, QFileDialog, QMessageBox, QGroupBox, QListWidget,
                            QListWidgetItem, QFrame, QSizePolicy, QSpacerItem, QSplitter)
from PyQt6.QtCore import Qt, QPropertyAnimation, QEasingCurve, QRect
from PyQt6.QtGui import QFont, QPalette, QColor, QLinearGradient, QPainter, QPixmap, QIcon

from core.data_processor import DataProcessor
from utils.resource_manager import resource_manager


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.data_processor = None
        self.allocation_data = {}  # 存储分配数据用于搜索
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("评价任务拆分工具")
        self.setGeometry(100, 100, 850, 850)
        self.setMinimumSize(850, 850)

        # 设置窗口图标
        window_icon = resource_manager.get_app_icon()
        if not window_icon.isNull():
            self.setWindowIcon(window_icon)

        # 设置窗口样式
        self.setStyleSheet(self.get_main_stylesheet())

        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 删除标题区域
        
        # 文件选择区域
        file_group = self.create_file_selection_section()
        main_layout.addWidget(file_group)


        # 控制按钮区域
        button_section = self.create_button_section()
        main_layout.addWidget(button_section)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet(self.get_progress_bar_stylesheet())
        main_layout.addWidget(self.progress_bar)

        # 状态显示区域
        status_section = self.create_status_section()
        main_layout.addWidget(status_section)
        
        # 存储文件路径
        self.assignment_file = ""
        self.daily_file = ""
        self.operation_files = []
        self.output_folder = ""
    
    def select_assignment_file(self):
        """选择客服排班文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择客服排班", "", "Excel文件 (*.xlsx *.xls)")
        if file_path:
            self.assignment_file = file_path
            self.assignment_edit.setText(file_path)
    
    def select_daily_file(self):
        """选择每日工作总表文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择每日工作总表", "", "Excel文件 (*.xlsx *.xls)")
        if file_path:
            self.daily_file = file_path
            self.daily_edit.setText(file_path)
    
    def select_operation_files(self):
        """选择运营表格文件"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "选择运营表格文件", "", "Excel文件 (*.xlsx *.xls)")
        if file_paths:
            self.operation_files = file_paths
            # 清空列表并添加新文件
            self.operation_list.clear()
            for file_path in file_paths:
                item = QListWidgetItem(os.path.basename(file_path))
                self.operation_list.addItem(item)
    
    def select_output_folder(self):
        """选择输出目录"""
        folder_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if folder_path:
            self.output_folder = folder_path
            self.output_edit.setText(folder_path)
    
    def start_processing(self):
        """开始处理"""
        # 验证输入
        if not self.assignment_file:
            QMessageBox.warning(self, "警告", "请选择客服排班文件")
            return
        
        if not self.daily_file:
            QMessageBox.warning(self, "警告", "请选择每日工作总表文件")
            return
        
        if not self.operation_files:
            QMessageBox.warning(self, "警告", "请选择运营表格文件")
            return
        
        if not self.output_folder:
            QMessageBox.warning(self, "警告", "请选择输出目录")
            return
        
        # 清空状态显示
        self.status_text.clear()
        
        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # 禁用按钮
        self.start_btn.setEnabled(False)
        self.open_folder_btn.setEnabled(False)
        
        # 创建并启动数据处理器
        self.data_processor = DataProcessor(
            self.assignment_file,
            self.daily_file,
            self.operation_files,
            self.output_folder
        )
        
        # 连接信号
        self.data_processor.progress_updated.connect(self.update_progress)
        self.data_processor.status_updated.connect(self.update_status)
        self.data_processor.finished_signal.connect(self.processing_finished)
        self.data_processor.allocation_data_ready.connect(self.update_allocation_data)
        
        # 启动处理
        self.data_processor.start()
    
    def stop_processing(self):
        """停止处理"""
        if self.data_processor and self.data_processor.isRunning():
            self.data_processor.terminate()
            self.data_processor.wait()
            self.update_status("处理已停止")
            self.processing_finished(False, "用户停止了处理")
    
    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)
    
    def update_status(self, message):
        """更新状态显示"""
        # 添加时间戳
        timestamped_message = f"[{pd.Timestamp.now().strftime('%H:%M:%S')}] {message}"
        self.status_text.append(timestamped_message)
        # 自动滚动到底部
        cursor = self.status_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.status_text.setTextCursor(cursor)
    
    def processing_finished(self, success, message):
        """处理完成"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 恢复按钮状态
        self.start_btn.setEnabled(True)

        # 如果处理成功且有输出文件夹，启用打开文件夹按钮
        if success and self.output_folder and os.path.exists(self.output_folder):
            self.open_folder_btn.setEnabled(True)

        # 显示结果消息
        if success:
            QMessageBox.information(self, "成功", message)
        else:
            QMessageBox.critical(self, "错误", message)

    def open_output_folder(self):
        """打开输出文件夹"""
        if not self.output_folder:
            QMessageBox.warning(self, "警告", "未设置输出文件夹")
            return

        if not os.path.exists(self.output_folder):
            QMessageBox.warning(self, "警告", f"输出文件夹不存在: {self.output_folder}")
            return

        try:
            # 根据操作系统选择合适的命令
            import platform
            system = platform.system()

            if system == "Windows":
                # Windows系统使用explorer
                os.startfile(self.output_folder)
            elif system == "Darwin":  # macOS
                # macOS系统使用open
                os.system(f'open "{self.output_folder}"')
            else:  # Linux和其他Unix系统
                # Linux系统使用xdg-open
                os.system(f'xdg-open "{self.output_folder}"')

            self.update_status(f"已打开输出文件夹: {self.output_folder}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开文件夹: {str(e)}")
            self.update_status(f"打开文件夹失败: {str(e)}")

    def get_main_stylesheet(self):
        """获取主窗口样式表"""
        return """
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f0f4f8, stop:1 #e2e8f0);
                color: #2d3748;
            }

            QWidget {
                font-family: 'Microsoft YaHei', 'Segoe UI', Arial, sans-serif;
                font-size: 10pt;
            }

            QLabel {
                color: #2d3748;
                font-weight: 500;
            }

            QLineEdit {
                background-color: white;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                padding: 12px 16px;
                font-size: 10pt;
                color: #2d3748;
            }

            QLineEdit:focus {
                border-color: #4299e1;
                background-color: #f7fafc;
            }

            QLineEdit:hover {
                border-color: #cbd5e0;
            }

            QTextEdit {
                background-color: white;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                padding: 12px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 9pt;
                color: #2d3748;
                line-height: 1.4;
            }

            QListWidget {
                background-color: white;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                padding: 8px;
                alternate-background-color: #f7fafc;
            }

            QListWidget::item {
                padding: 8px 12px;
                border-radius: 4px;
                margin: 2px 0;
            }

            QListWidget::item:selected {
                background-color: #4299e1;
                color: white;
            }

            QListWidget::item:hover {
                background-color: #e2e8f0;
            }
        """

    def get_progress_bar_stylesheet(self):
        """获取进度条样式表"""
        return """
            QProgressBar {
                background-color: #e2e8f0;
                border: none;
                border-radius: 12px;
                height: 24px;
                text-align: center;
                font-weight: bold;
                color: #2d3748;
            }

            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4299e1, stop:1 #3182ce);
                border-radius: 12px;
                margin: 2px;
            }
        """

    def create_title_section(self):
        """创建标题区域"""
        title_frame = QFrame()
        title_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 16px;
                padding: 15px;
                margin: 5px 0;
                min-height: 80px;
            }
        """)

        title_layout = QVBoxLayout(title_frame)
        title_layout.setSpacing(8)
        title_layout.setContentsMargins(15, 15, 15, 15)

        # 主标题
        main_title = QLabel("📊 评价任务拆分工具")
        main_title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18pt;
                font-weight: bold;
                background: transparent;
                padding: 8px 0;
            }
        """)
        main_title.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 副标题
        sub_title = QLabel("智能分配 • 高效处理 • 专业工具")
        sub_title.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.9);
                font-size: 11pt;
                font-weight: 400;
                background: transparent;
                padding: 5px 0;
            }
        """)
        sub_title.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # 版本信息
        version_label = QLabel("v2.0")
        version_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 9pt;
                background: transparent;
                padding: 5px 0;
            }
        """)
        version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        title_layout.addWidget(main_title)
        title_layout.addWidget(sub_title)
        title_layout.addWidget(version_label)

        return title_frame

    def create_file_selection_section(self):
        """创建文件选择区域"""
        file_group = QGroupBox("📁 文件选择")
        file_group.setStyleSheet("""
            QGroupBox {
                font-size: 14pt;
                font-weight: bold;
                color: #2d3748;
                border: 2px solid #e2e8f0;
                border-radius: 12px;
                margin-top: 20px;
                padding-top: 15px;
                background-color: white;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                background-color: white;
                color: #4a5568;
            }
        """)

        file_layout = QVBoxLayout(file_group)
        file_layout.setSpacing(15)
        file_layout.setContentsMargins(25, 25, 25, 25)

        # 客服排班选择
        assignment_section = self.create_file_input_row(
            "👥 客服排班:",
            "选择客服排班Excel文件...",
            self.select_assignment_file,
            "#e53e3e"
        )
        self.assignment_edit = assignment_section['edit']
        file_layout.addLayout(assignment_section['layout'])

        # 每日工作总表选择
        daily_section = self.create_file_input_row(
            "📋 每日工作总表:",
            "选择每日工作总表Excel文件...",
            self.select_daily_file,
            "#38a169"
        )
        self.daily_edit = daily_section['edit']
        file_layout.addLayout(daily_section['layout'])

        # 运营表格选择
        operation_layout = QVBoxLayout()
        operation_btn_layout = QHBoxLayout()

        operation_label = QLabel("📊 运营表格:")
        operation_label.setStyleSheet("font-weight: bold; color: #2d3748; font-size: 11pt;")
        operation_btn_layout.addWidget(operation_label)

        operation_btn = QPushButton("🔍 选择运营表格(可多选)")
        operation_btn.clicked.connect(self.select_operation_files)
        operation_btn.setStyleSheet(self.get_file_button_stylesheet("#3182ce"))
        operation_btn_layout.addWidget(operation_btn)

        operation_layout.addLayout(operation_btn_layout)

        # 文件列表显示
        self.operation_list = QListWidget()
        self.operation_list.setMaximumHeight(120)
        self.operation_list.setMinimumHeight(100)
        self.operation_list.setStyleSheet("""
            QListWidget {
                background-color: #f7fafc;
                border: 2px dashed #cbd5e0;
                border-radius: 8px;
                padding: 10px;
                font-size: 9pt;
            }

            QListWidget::item {
                padding: 6px 10px;
                border-radius: 4px;
                margin: 1px 0;
                background-color: white;
                border: 1px solid #e2e8f0;
            }

            QListWidget::item:selected {
                background-color: #4299e1;
                color: white;
                border-color: #3182ce;
            }
        """)
        operation_layout.addWidget(self.operation_list)

        file_layout.addLayout(operation_layout)

        # 输出目录选择
        output_section = self.create_file_input_row(
            "📤 输出目录:",
            "选择输出目录...",
            self.select_output_folder,
            "#805ad5"
        )
        self.output_edit = output_section['edit']
        file_layout.addLayout(output_section['layout'])

        return file_group

    def create_file_input_row(self, label_text, placeholder, callback, color):
        """创建文件输入行"""
        layout = QHBoxLayout()
        layout.setSpacing(10)

        # 标签
        label = QLabel(label_text)
        label.setStyleSheet(f"""
            QLabel {{
                font-weight: bold;
                color: #2d3748;
                font-size: 9pt;
                min-width: 100px;
            }}
        """)
        label.setAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        layout.addWidget(label)

        # 输入框
        edit = QLineEdit()
        edit.setPlaceholderText(placeholder)
        edit.setStyleSheet(f"""
            QLineEdit {{
                background-color: white;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 9pt;
                color: #2d3748;
                min-height: 20px;
                max-height: 32px;
            }}

            QLineEdit:focus {{
                border-color: {color};
                background-color: #f7fafc;
            }}

            QLineEdit:hover {{
                border-color: #cbd5e0;
            }}
        """)
        layout.addWidget(edit, 1)

        # 浏览按钮
        btn = QPushButton("📂 浏览")
        btn.clicked.connect(callback)
        btn.setStyleSheet(self.get_file_button_stylesheet(color))
        layout.addWidget(btn)

        return {'layout': layout, 'edit': edit, 'button': btn}

    def get_file_button_stylesheet(self, color):
        """获取文件按钮样式表"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color}, stop:1 {self.darken_color(color)});
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 16px;
                font-size: 9pt;
                font-weight: bold;
                min-width: 90px;
            }}

            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {self.lighten_color(color)}, stop:1 {color});
            }}

            QPushButton:pressed {{
                background: {self.darken_color(color)};
            }}
        """

    def darken_color(self, color):
        """使颜色变暗"""
        color_map = {
            "#e53e3e": "#c53030",
            "#38a169": "#2f855a",
            "#3182ce": "#2c5282",
            "#805ad5": "#6b46c1"
        }
        return color_map.get(color, color)

    def lighten_color(self, color):
        """使颜色变亮"""
        color_map = {
            "#e53e3e": "#f56565",
            "#38a169": "#48bb78",
            "#3182ce": "#4299e1",
            "#805ad5": "#9f7aea"
        }
        return color_map.get(color, color)

    def create_button_section(self):
        """创建按钮区域"""
        button_frame = QFrame()
        button_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.8);
                border-radius: 12px;
                padding: 8px;
                margin: 5px 0;
                min-height: 40px;
            }
        """)

        button_layout = QHBoxLayout(button_frame)
        button_layout.setSpacing(15)
        button_layout.setContentsMargins(10, 8, 10, 8)

        # 添加弹性空间
        button_layout.addStretch()

        # 开始拆分按钮
        self.start_btn = QPushButton("🚀 开始拆分")
        self.start_btn.clicked.connect(self.start_processing)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #48bb78, stop:1 #38a169);
                color: white;
                border: none;
                border-radius: 12px;
                padding: 8px 16px;
                font-size: 9pt;
                font-weight: bold;
                min-width: 100px;
            }

            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #68d391, stop:1 #48bb78);
            }

            QPushButton:pressed {
                background: #2f855a;
            }

            QPushButton:disabled {
                background: #e2e8f0;
                color: #a0aec0;
            }
        """)
        button_layout.addWidget(self.start_btn)

        # 打开输出文件夹按钮
        self.open_folder_btn = QPushButton("📁 打开输出文件夹")
        self.open_folder_btn.clicked.connect(self.open_output_folder)
        self.open_folder_btn.setEnabled(False)
        self.open_folder_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4299e1, stop:1 #3182ce);
                color: white;
                border: none;
                border-radius: 12px;
                padding: 8px 16px;
                font-size: 9pt;
                font-weight: bold;
                min-width: 100px;
            }

            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #63b3ed, stop:1 #4299e1);
            }

            QPushButton:pressed {
                background: #2c5282;
            }

            QPushButton:disabled {
                background: #e2e8f0;
                color: #a0aec0;
            }
        """)
        button_layout.addWidget(self.open_folder_btn)

        # 添加弹性空间
        button_layout.addStretch()

        return button_frame

    def create_status_section(self):
        """创建状态显示区域"""
        status_group = QGroupBox("📊 处理状态")
        status_group.setStyleSheet("""
            QGroupBox {
                font-size: 14pt;
                font-weight: bold;
                color: #2d3748;
                border: 2px solid #e2e8f0;
                border-radius: 12px;
                margin-top: 20px;
                padding-top: 15px;
                background-color: white;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                background-color: white;
                color: #4a5568;
            }
        """)

        status_layout = QVBoxLayout(status_group)
        status_layout.setContentsMargins(20, 25, 20, 20)

        # 添加搜索区域
        search_layout = QHBoxLayout()
        search_layout.setSpacing(10)

        # 搜索标签
        search_label = QLabel("🔍 运营编码搜索:")
        search_label.setStyleSheet("""
            QLabel {
                color: #4a5568;
                font-size: 10pt;
                font-weight: 500;
            }
        """)
        search_layout.addWidget(search_label)

        # 搜索输入框
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入运营编码查询拆分情况...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                background-color: white;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 9pt;
                color: #2d3748;
                min-height: 20px;
            }

            QLineEdit:focus {
                border-color: #4299e1;
                background-color: #f7fafc;
            }

            QLineEdit:hover {
                border-color: #cbd5e0;
            }
        """)
        self.search_input.returnPressed.connect(self.search_operation_code)
        search_layout.addWidget(self.search_input, 1)

        # 搜索按钮
        search_btn = QPushButton("搜索")
        search_btn.clicked.connect(self.search_operation_code)
        search_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4299e1, stop:1 #3182ce);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 9pt;
                font-weight: 500;
                min-width: 60px;
            }

            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3182ce, stop:1 #2c5aa0);
            }

            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2c5aa0, stop:1 #2a4d8d);
            }
        """)
        search_layout.addWidget(search_btn)

        # 清空按钮
        clear_btn = QPushButton("清空")
        clear_btn.clicked.connect(self.clear_search)
        clear_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #718096, stop:1 #4a5568);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 9pt;
                font-weight: 500;
                min-width: 60px;
            }

            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4a5568, stop:1 #2d3748);
            }

            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2d3748, stop:1 #1a202c);
            }
        """)
        search_layout.addWidget(clear_btn)

        status_layout.addLayout(search_layout)

        # 创建分割器来分隔状态显示和搜索结果
        splitter = QSplitter(Qt.Orientation.Horizontal)

        # 状态显示文本框
        self.status_text = QTextEdit()
        self.status_text.setReadOnly(True)
        self.status_text.setMaximumHeight(150)
        self.status_text.setMinimumHeight(120)
        self.status_text.setStyleSheet("""
            QTextEdit {
                background-color: #1a202c;
                border: 2px solid #2d3748;
                border-radius: 8px;
                padding: 15px;
                font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                font-size: 9pt;
                color: #e2e8f0;
                line-height: 1.5;
            }

            QScrollBar:vertical {
                background-color: #2d3748;
                width: 12px;
                border-radius: 6px;
            }

            QScrollBar::handle:vertical {
                background-color: #4a5568;
                border-radius: 6px;
                min-height: 20px;
            }

            QScrollBar::handle:vertical:hover {
                background-color: #718096;
            }
        """)

        # 搜索结果显示文本框
        self.search_result_text = QTextEdit()
        self.search_result_text.setReadOnly(True)
        self.search_result_text.setMaximumHeight(150)
        self.search_result_text.setMinimumHeight(120)
        self.search_result_text.setStyleSheet("""
            QTextEdit {
                background-color: #f7fafc;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                padding: 15px;
                font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                font-size: 9pt;
                color: #2d3748;
                line-height: 1.5;
            }

            QScrollBar:vertical {
                background-color: #e2e8f0;
                width: 12px;
                border-radius: 6px;
            }

            QScrollBar::handle:vertical {
                background-color: #cbd5e0;
                border-radius: 6px;
                min-height: 20px;
            }

            QScrollBar::handle:vertical:hover {
                background-color: #a0aec0;
            }
        """)
        self.search_result_text.setPlaceholderText("搜索结果将在这里显示...")

        # 添加到分割器
        splitter.addWidget(self.status_text)
        splitter.addWidget(self.search_result_text)
        splitter.setSizes([1, 1])  # 平分空间

        # 添加初始欢迎消息
        welcome_msg = """
🎉 欢迎使用评价任务拆分工具-by：小陈

✨ 新功能特性：
• 🚀 智能平衡分配算法
• 🔧 通用运营编码识别
• 📅 日期后缀自动处理
• 🔗 超链接跳转功能
• 🔍 运营编码搜索功能

📋 使用步骤：
1. 选择客服排班Excel文件
2. 选择每日工作总表Excel文件
3. 选择运营表格文件(可多选)
4. 选择输出目录
5. 点击"开始拆分"按钮

准备就绪，等待您的操作...
        """
        self.status_text.setPlainText(welcome_msg.strip())

        # 添加搜索结果初始消息
        search_welcome_msg = """
🔍 运营编码搜索

使用方法：
• 在搜索框中输入运营编码
• 支持模糊搜索和部分匹配
• 显示分配给哪个客服
• 显示数据量和处理状态

等待拆分完成后即可搜索...
        """
        self.search_result_text.setPlainText(search_welcome_msg.strip())

        status_layout.addWidget(splitter)

        return status_group

    def search_operation_code(self):
        """搜索运营编码"""
        search_term = self.search_input.text().strip()
        if not search_term:
            QMessageBox.warning(self, "提示", "请输入要搜索的运营编码")
            return

        if not self.allocation_data:
            self.search_result_text.setPlainText("❌ 暂无拆分数据，请先完成拆分操作")
            return

        # 执行搜索
        results = self.perform_search(search_term)

        # 显示搜索结果
        self.display_search_results(search_term, results)

    def perform_search(self, search_term):
        """执行搜索操作"""
        results = []
        search_term_lower = search_term.lower()

        # 遍历所有分配数据
        for staff_name, staff_data in self.allocation_data.items():
            for operation_code, data_info in staff_data.items():
                # 检查运营编码是否匹配（支持模糊搜索）
                if search_term_lower in operation_code.lower():
                    results.append({
                        'staff_name': staff_name,
                        'operation_code': operation_code,
                        'data_count': data_info.get('data_count', 0),
                        'phone_name': data_info.get('phone_name', '未知'),
                        'match_type': 'exact' if search_term_lower == operation_code.lower() else 'partial'
                    })

        # 按匹配类型和数据量排序
        results.sort(key=lambda x: (x['match_type'] == 'partial', -x['data_count']))
        return results

    def display_search_results(self, search_term, results):
        """显示搜索结果"""
        if not results:
            result_text = f"""
🔍 搜索结果: "{search_term}"

❌ 未找到匹配的运营编码

建议：
• 检查运营编码是否正确
• 尝试输入部分编码进行模糊搜索
• 确认该编码已在拆分数据中
            """
        else:
            result_text = f"""
🔍 搜索结果: "{search_term}"

✅ 找到 {len(results)} 个匹配项：

"""
            for i, result in enumerate(results, 1):
                match_icon = "🎯" if result['match_type'] == 'exact' else "🔍"
                result_text += f"""
{match_icon} 匹配 {i}:
  运营编码: {result['operation_code']}
  分配客服: {result['staff_name']}
  数据量: {result['data_count']} 条
  手机名称: {result['phone_name']}
  匹配类型: {'精确匹配' if result['match_type'] == 'exact' else '模糊匹配'}
"""

        self.search_result_text.setPlainText(result_text.strip())

    def clear_search(self):
        """清空搜索"""
        self.search_input.clear()
        search_welcome_msg = """
🔍 运营编码搜索

使用方法：
• 在搜索框中输入运营编码
• 支持模糊搜索和部分匹配
• 显示分配给哪个客服
• 显示数据量和处理状态

等待拆分完成后即可搜索...
        """
        self.search_result_text.setPlainText(search_welcome_msg.strip())

    def update_allocation_data(self, allocation_data):
        """更新分配数据用于搜索"""
        self.allocation_data = allocation_data
        # 更新搜索结果区域提示
        if allocation_data:
            search_ready_msg = """
🔍 运营编码搜索

✅ 拆分数据已加载，可以开始搜索！

使用方法：
• 在搜索框中输入运营编码
• 支持模糊搜索和部分匹配
• 显示分配给哪个客服
• 显示数据量和处理状态

输入运营编码开始搜索...
            """
            self.search_result_text.setPlainText(search_ready_msg.strip())
